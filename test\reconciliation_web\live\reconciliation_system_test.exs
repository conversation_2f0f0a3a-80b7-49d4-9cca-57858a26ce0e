defmodule ReconciliationWeb.ReconciliationSystemTest do
  use ReconciliationWeb.ConnCase, async: true
  import Phoenix.LiveViewTest

  alias Reconciliation.{Accounts, Reconciliation}

  describe "Reconciliation System Integration" do
    setup do
      # Create a test user
      user_attrs = %{
        email: "<EMAIL>",
        password: "hello world!"
      }
      
      {:ok, user} = Accounts.register_user(user_attrs)
      
      %{user: user}
    end

    test "navigation links work correctly", %{conn: conn, user: user} do
      # Log in the user
      conn = log_in_user(conn, user)
      
      # Test dashboard navigation
      {:ok, _view, html} = live(conn, "/dashboard")
      assert html =~ "Financial Overview Dashboard"
      
      # Test reconciliation navigation
      {:ok, _view, html} = live(conn, "/reconciliation")
      assert html =~ "Upload Files for Reconciliation"
      
      # Test transactions navigation
      {:ok, _view, html} = live(conn, "/transactions")
      assert html =~ "Transactions"
      
      # Test reports navigation
      {:ok, _view, html} = live(conn, "/reports")
      assert html =~ "Reports & Analytics"
      
      # Test settings navigation
      {:ok, _view, html} = live(conn, "/settings")
      assert html =~ "Reconciliation Settings"
    end

    test "reconciliation run creation works", %{user: user} do
      # Test creating a reconciliation run
      run_attrs = %{
        name: "Test Reconciliation",
        description: "Test run for integration testing",
        user_id: user.id,
        status: "pending"
      }
      
      {:ok, run} = Reconciliation.create_reconciliation_run(run_attrs)
      
      assert run.name == "Test Reconciliation"
      assert run.user_id == user.id
      assert run.status == "pending"
      
      # Test retrieving runs for user
      runs = Reconciliation.list_reconciliation_runs(user.id)
      assert length(runs) == 1
      assert hd(runs).id == run.id
    end

    test "settings creation and update works", %{user: user} do
      # Test getting or creating settings
      {:ok, settings} = Reconciliation.get_or_create_settings(user.id)
      
      assert settings.user_id == user.id
      assert settings.amount_tolerance == Decimal.new("0.01")
      assert settings.auto_match_exact == true
      
      # Test updating settings
      update_attrs = %{
        amount_tolerance: Decimal.new("0.05"),
        auto_match_exact: false,
        default_currency: "EUR"
      }
      
      {:ok, updated_settings} = Reconciliation.update_settings(settings, update_attrs)
      
      assert updated_settings.amount_tolerance == Decimal.new("0.05")
      assert updated_settings.auto_match_exact == false
      assert updated_settings.default_currency == "EUR"
    end

    test "file upload and transaction creation workflow", %{user: user} do
      # Create a reconciliation run
      {:ok, run} = Reconciliation.create_reconciliation_run(%{
        name: "File Upload Test",
        user_id: user.id,
        status: "pending"
      })
      
      # Create uploaded files
      {:ok, file_a} = Reconciliation.create_uploaded_file(%{
        reconciliation_run_id: run.id,
        file_type: "file_a",
        filename: "test_file_a.csv",
        file_size: 1024,
        status: "uploaded"
      })
      
      {:ok, file_b} = Reconciliation.create_uploaded_file(%{
        reconciliation_run_id: run.id,
        file_type: "file_b", 
        filename: "test_file_b.csv",
        file_size: 1024,
        status: "uploaded"
      })
      
      # Create test transactions
      transaction_attrs = [
        %{
          reconciliation_run_id: run.id,
          uploaded_file_id: file_a.id,
          transaction_date: ~D[2024-01-15],
          amount: Decimal.new("-1500.00"),
          reference: "REF12345",
          description: "Test Transaction A",
          row_number: 1,
          is_matched: false
        },
        %{
          reconciliation_run_id: run.id,
          uploaded_file_id: file_b.id,
          transaction_date: ~D[2024-01-15],
          amount: Decimal.new("-1500.00"),
          reference: "REF12345",
          description: "Test Transaction B",
          row_number: 1,
          is_matched: false
        }
      ]
      
      # Insert transactions
      {2, transactions} = Reconciliation.create_transactions(transaction_attrs)
      
      assert length(transactions) == 2
      
      # Test retrieving transactions
      retrieved_transactions = Reconciliation.get_transactions(run.id)
      assert length(retrieved_transactions) == 2
      
      # Test creating a transaction match
      [txn_a, txn_b] = retrieved_transactions
      
      {:ok, match} = Reconciliation.create_transaction_match(%{
        reconciliation_run_id: run.id,
        transaction_a_id: txn_a.id,
        transaction_b_id: txn_b.id,
        match_type: "exact",
        confidence_score: Decimal.new("1.0"),
        is_verified: false
      })
      
      assert match.match_type == "exact"
      assert match.confidence_score == Decimal.new("1.0")
      
      # Test retrieving matches
      matches = Reconciliation.get_transaction_matches(run.id)
      assert length(matches) == 1
      assert hd(matches).id == match.id
    end
  end
end
