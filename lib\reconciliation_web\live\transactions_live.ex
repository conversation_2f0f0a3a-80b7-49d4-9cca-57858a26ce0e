defmodule ReconciliationWeb.TransactionsLive do
  use ReconciliationWeb, :live_view

  alias Reconciliation.{Transaction, ReconciliationRun}

  @impl true
  def mount(_params, _session, socket) do
    user = socket.assigns.current_user
    
    # Get all transactions for the user's reconciliation runs
    transactions = get_user_transactions(user.id)
    reconciliation_runs = Reconciliation.list_reconciliation_runs(user.id)
    
    {:ok,
     socket
     |> assign(:page_title, "Transactions")
     |> assign(:transactions, transactions)
     |> assign(:reconciliation_runs, reconciliation_runs)
     |> assign(:selected_run_id, nil)
     |> assign(:search_query, "")
    }
  end

  @impl true
  def handle_event("filter_by_run", %{"run_id" => ""}, socket) do
    user = socket.assigns.current_user
    transactions = get_user_transactions(user.id)
    
    {:noreply,
     socket
     |> assign(:transactions, transactions)
     |> assign(:selected_run_id, nil)
    }
  end

  def handle_event("filter_by_run", %{"run_id" => run_id}, socket) do
    transactions = Reconciliation.get_transactions(run_id)
    
    {:noreply,
     socket
     |> assign(:transactions, transactions)
     |> assign(:selected_run_id, run_id)
    }
  end

  def handle_event("search", %{"search" => query}, socket) do
    user = socket.assigns.current_user
    transactions = if socket.assigns.selected_run_id do
      Reconciliation.get_transactions(socket.assigns.selected_run_id)
    else
      get_user_transactions(user.id)
    end
    
    filtered_transactions = filter_transactions(transactions, query)
    
    {:noreply,
     socket
     |> assign(:transactions, filtered_transactions)
     |> assign(:search_query, query)
    }
  end

  # Helper functions
  defp get_user_transactions(user_id) do
    user_id
    |> Reconciliation.list_reconciliation_runs()
    |> Enum.flat_map(&Reconciliation.get_transactions(&1.id))
    |> Enum.sort_by(& &1.inserted_at, {:desc, DateTime})
  end

  defp filter_transactions(transactions, ""), do: transactions
  defp filter_transactions(transactions, query) do
    query_lower = String.downcase(query)
    
    Enum.filter(transactions, fn transaction ->
      String.contains?(String.downcase(transaction.description || ""), query_lower) ||
      String.contains?(String.downcase(transaction.reference || ""), query_lower) ||
      String.contains?(String.downcase(to_string(transaction.amount)), query_lower)
    end)
  end

  defp format_currency(amount) when is_nil(amount), do: "$0.00"
  defp format_currency(amount) do
    "$#{Decimal.to_string(amount, :normal)}"
  end

  defp format_date(nil), do: "-"
  defp format_date(date), do: Calendar.strftime(date, "%b %d, %Y")

  defp match_status_badge(true) do
    assigns = %{}
    ~H"""
    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
      Matched
    </span>
    """
  end

  defp match_status_badge(false) do
    assigns = %{}
    ~H"""
    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">
      Unmatched
    </span>
    """
  end

  defp file_type_badge("file_a") do
    assigns = %{}
    ~H"""
    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">
      File A
    </span>
    """
  end

  defp file_type_badge("file_b") do
    assigns = %{}
    ~H"""
    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
      File B
    </span>
    """
  end

  defp file_type_badge(_) do
    assigns = %{}
    ~H"""
    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800">
      Unknown
    </span>
    """
  end
end
