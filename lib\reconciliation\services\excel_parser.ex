defmodule Reconciliation.Services.ExcelParser do
  @moduledoc """
  Service for parsing Excel and CSV files and extracting transaction data.
  """

  alias Reconciliation.{
    UploadedFile,
    Transaction
  }

  require Logger

  @doc """
  Parses an uploaded file and extracts transaction data.
  """
  def parse_file(%UploadedFile{} = uploaded_file) do
    Logger.info("Starting to parse file: #{uploaded_file.filename}")

    # Update status to processing
    {:ok, uploaded_file} = Reconciliation.update_uploaded_file(uploaded_file, %{
      status: "processing"
    })

    try do
      case get_file_type(uploaded_file) do
        :excel -> parse_excel_file(uploaded_file)
        :csv -> parse_csv_file(uploaded_file)
        :unknown -> {:error, "Unsupported file format"}
      end
    rescue
      error ->
        Logger.error("Error parsing file #{uploaded_file.filename}: #{inspect(error)}")
        Reconciliation.mark_file_failed(uploaded_file, [Exception.message(error)])
        {:error, Exception.message(error)}
    end
  end

  @doc """
  Parse uploaded file with progress tracking.
  """
  def parse_file_with_progress(%UploadedFile{} = uploaded_file, progress_callback) do
    Logger.info("Starting to parse file with progress: #{uploaded_file.filename}")

    # Update status to processing
    {:ok, uploaded_file} = Reconciliation.update_uploaded_file(uploaded_file, %{
      status: "processing"
    })

    try do
      case get_file_type(uploaded_file) do
        :excel -> parse_excel_file_with_progress(uploaded_file, progress_callback)
        :csv -> parse_csv_file_with_progress(uploaded_file, progress_callback)
        :unknown -> {:error, "Unsupported file format"}
      end
    rescue
      error ->
        Logger.error("Error parsing file #{uploaded_file.filename}: #{inspect(error)}")
        Reconciliation.mark_file_failed(uploaded_file, [Exception.message(error)])
        {:error, Exception.message(error)}
    end
  end

  @doc """
  Determines the file type based on extension.
  """
  def get_file_type(%UploadedFile{filename: filename}) do
    case Path.extname(filename) |> String.downcase() do
      ext when ext in [".xlsx", ".xls"] -> :excel
      ".csv" -> :csv
      _ -> :unknown
    end
  end

  # Parse Excel files using xlsxir
  defp parse_excel_file(%UploadedFile{} = uploaded_file) do
    Logger.info("Parsing Excel file: #{uploaded_file.filename}")

    case Xlsxir.multi_extract(uploaded_file.file_path) do
      {:ok, table_ids} ->
        # Use the first sheet
        [table_id | _] = table_ids

        # Get all rows
        rows = Xlsxir.get_list(table_id)

        # Clean up
        Xlsxir.close(table_id)

        process_rows(uploaded_file, rows)

      {:error, reason} ->
        error_msg = "Failed to parse Excel file: #{inspect(reason)}"
        Logger.error(error_msg)
        Reconciliation.mark_file_failed(uploaded_file, [error_msg])
        {:error, error_msg}
    end
  end

  # Parse Excel files with progress tracking
  defp parse_excel_file_with_progress(%UploadedFile{} = uploaded_file, progress_callback) do
    Logger.info("Parsing Excel file: #{uploaded_file.filename}")

    progress_callback.(%{
      status: "parsing",
      progress: 10,
      message: "Reading Excel file..."
    })

    case Xlsxir.multi_extract(uploaded_file.file_path) do
      {:ok, table_ids} ->
        progress_callback.(%{
          status: "parsing",
          progress: 30,
          message: "Extracting worksheet data..."
        })

        # Use the first sheet
        [table_id | _] = table_ids

        # Get all rows
        rows = Xlsxir.get_list(table_id)

        # Clean up
        Xlsxir.close(table_id)

        progress_callback.(%{
          status: "parsing",
          progress: 50,
          message: "Processing rows..."
        })

        process_rows_with_progress(uploaded_file, rows, progress_callback)

      {:error, reason} ->
        error_msg = "Failed to parse Excel file: #{inspect(reason)}"
        Logger.error(error_msg)
        Reconciliation.mark_file_failed(uploaded_file, [error_msg])
        {:error, error_msg}
    end
  end

  # Parse CSV files using NimbleCSV
  defp parse_csv_file(%UploadedFile{} = uploaded_file) do
    Logger.info("Parsing CSV file: #{uploaded_file.filename}")

    case File.read(uploaded_file.file_path) do
      {:ok, content} ->
        # Define CSV parser
        alias NimbleCSV.RFC4180, as: CSV

        rows = content
        |> CSV.parse_string(skip_headers: false)
        |> Enum.to_list()

        process_rows(uploaded_file, rows)

      {:error, reason} ->
        error_msg = "Failed to read CSV file: #{inspect(reason)}"
        Logger.error(error_msg)
        Reconciliation.mark_file_failed(uploaded_file, [error_msg])
        {:error, error_msg}
    end
  end

  # Parse CSV files with progress tracking
  defp parse_csv_file_with_progress(%UploadedFile{} = uploaded_file, progress_callback) do
    Logger.info("Parsing CSV file: #{uploaded_file.filename}")

    progress_callback.(%{
      status: "parsing",
      progress: 10,
      message: "Reading CSV file..."
    })

    case File.read(uploaded_file.file_path) do
      {:ok, content} ->
        progress_callback.(%{
          status: "parsing",
          progress: 30,
          message: "Parsing CSV content..."
        })

        # Define CSV parser
        alias NimbleCSV.RFC4180, as: CSV

        rows = content
        |> CSV.parse_string(skip_headers: false)
        |> Enum.to_list()

        progress_callback.(%{
          status: "parsing",
          progress: 50,
          message: "Processing rows..."
        })

        process_rows_with_progress(uploaded_file, rows, progress_callback)

      {:error, reason} ->
        error_msg = "Failed to read CSV file: #{inspect(reason)}"
        Logger.error(error_msg)
        Reconciliation.mark_file_failed(uploaded_file, [error_msg])
        {:error, error_msg}
    end
  end

  # Process rows and extract transaction data
  defp process_rows(%UploadedFile{} = uploaded_file, []) do
    error_msg = "File is empty or contains no data"
    Reconciliation.mark_file_failed(uploaded_file, [error_msg])
    {:error, error_msg}
  end

  defp process_rows(%UploadedFile{} = uploaded_file, [headers | data_rows]) do
    Logger.info("Processing #{length(data_rows)} data rows")

    # Detect column mapping
    column_mapping = detect_column_mapping(headers)

    # Update file with detected headers and mapping
    {:ok, uploaded_file} = Reconciliation.update_uploaded_file(uploaded_file, %{
      headers_detected: headers,
      column_mapping: column_mapping,
      total_rows: length(data_rows)
    })

    # Process each row
    {transactions, errors} = process_data_rows(data_rows, column_mapping, uploaded_file)

    # Update file status
    final_status = if length(errors) == length(data_rows), do: "failed", else: "processed"

    {:ok, _} = Reconciliation.update_uploaded_file(uploaded_file, %{
      status: final_status,
      processed_rows: length(transactions),
      error_rows: length(errors),
      processing_errors: errors,
      processed_at: DateTime.utc_now()
    })

    if final_status == "processed" do
      Logger.info("Successfully processed #{length(transactions)} transactions")
      {:ok, transactions}
    else
      Logger.error("Failed to process any transactions from file")
      {:error, "Failed to process transactions: #{Enum.join(errors, ", ")}"}
    end
  end

  # Process rows with progress tracking
  defp process_rows_with_progress(%UploadedFile{} = uploaded_file, [], _progress_callback) do
    error_msg = "File is empty or contains no data"
    Reconciliation.mark_file_failed(uploaded_file, [error_msg])
    {:error, error_msg}
  end

  defp process_rows_with_progress(%UploadedFile{} = uploaded_file, [headers | data_rows], progress_callback) do
    total_rows = length(data_rows)
    Logger.info("Processing #{total_rows} data rows")

    progress_callback.(%{
      status: "processing",
      progress: 60,
      message: "Detecting column mapping...",
      total_records: total_rows,
      records_processed: 0
    })

    # Detect column mapping
    column_mapping = detect_column_mapping(headers)

    # Update file with detected headers and mapping
    {:ok, uploaded_file} = Reconciliation.update_uploaded_file(uploaded_file, %{
      headers_detected: headers,
      column_mapping: column_mapping,
      total_rows: total_rows
    })

    progress_callback.(%{
      status: "processing",
      progress: 70,
      message: "Processing transaction data...",
      total_records: total_rows,
      records_processed: 0
    })

    # Process each row with progress tracking
    {transactions, errors} = process_data_rows_with_progress(data_rows, column_mapping, uploaded_file, progress_callback)

    # Update file status
    final_status = if length(errors) == length(data_rows), do: "failed", else: "processed"

    {:ok, _} = Reconciliation.update_uploaded_file(uploaded_file, %{
      status: final_status,
      processed_rows: length(transactions),
      error_rows: length(errors),
      processing_errors: errors,
      processed_at: DateTime.utc_now()
    })

    if final_status == "processed" do
      Logger.info("Successfully processed #{length(transactions)} transactions")
      {:ok, %{
        transactions: transactions,
        rows_inserted: length(transactions),
        processing_time: 0  # This will be updated by the progress callback
      }}
    else
      Logger.error("Failed to process any transactions from file")
      {:error, "Failed to process transactions: #{Enum.join(errors, ", ")}"}
    end
  end

  # Detect column mapping based on headers
  defp detect_column_mapping(headers) do
    headers
    |> Enum.with_index()
    |> Enum.reduce(%{}, fn {header, index}, acc ->
      field = detect_field_type(header)
      if field, do: Map.put(acc, field, index), else: acc
    end)
  end

  # Detect field type based on header name
  defp detect_field_type(header) when is_binary(header) do
    header_lower = String.downcase(header)

    cond do
      header_lower in ["date", "transaction_date", "date_of_transaction", "trans_date"] -> "date"
      header_lower in ["amount", "transaction_amount", "amt", "value"] -> "amount"
      header_lower in ["debit"] -> "debit"
      header_lower in ["credit"] -> "credit"
      header_lower in ["reference", "ref", "transaction_reference", "reference_number", "ref_no"] -> "reference"
      header_lower in ["description", "details", "transaction_details", "memo", "narration"] -> "description"
      header_lower in ["id", "transaction_id", "unique_id", "trans_id"] -> "transaction_id"
      header_lower in ["type", "transaction_type", "debit_credit", "dr_cr"] -> "type"
      header_lower in ["account", "account_number", "account_name", "acc_no"] -> "account"
      header_lower in ["category", "transaction_category", "cat"] -> "category"
      true -> nil
    end
  end

  defp detect_field_type(_), do: nil

  # Process data rows and create transactions
  defp process_data_rows(data_rows, column_mapping, uploaded_file) do
    data_rows
    |> Enum.with_index(1)
    |> Enum.reduce({[], []}, fn {row, row_number}, {transactions, errors} ->
      case process_single_row(row, row_number, column_mapping, uploaded_file) do
        {:ok, transaction_attrs} ->
          case Reconciliation.create_transaction(transaction_attrs) do
            {:ok, transaction} -> {[transaction | transactions], errors}
            {:error, changeset} ->
              error_msg = "Row #{row_number}: #{format_changeset_errors(changeset)}"
              {transactions, [error_msg | errors]}
          end

        {:error, error_msg} ->
          {transactions, ["Row #{row_number}: #{error_msg}" | errors]}
      end
    end)
    |> then(fn {transactions, errors} -> {Enum.reverse(transactions), Enum.reverse(errors)} end)
  end

  # Process data rows with progress tracking
  defp process_data_rows_with_progress(data_rows, column_mapping, uploaded_file, progress_callback) do
    total_rows = length(data_rows)
    start_time = System.monotonic_time(:millisecond)

    progress_callback.(%{
      status: "processing",
      progress: 75,
      message: "Processing #{total_rows} transaction records...",
      total_records: total_rows,
      records_processed: 0
    })

    {transactions, errors} = data_rows
    |> Enum.with_index(1)
    |> Enum.reduce({[], []}, fn {row, row_number}, {transactions, errors} ->
      # Calculate progress
      progress_percent = min(75 + trunc((row_number / total_rows) * 20), 95)

      # Send progress update every 10 rows or for the last row
      if rem(row_number, 10) == 0 or row_number == total_rows do
        progress_callback.(%{
          status: "processing",
          progress: progress_percent,
          message: "Processing transaction #{row_number} of #{total_rows}...",
          total_records: total_rows,
          records_processed: row_number
        })
      end

      case process_single_row(row, row_number, column_mapping, uploaded_file) do
        {:ok, transaction_attrs} ->
          case Reconciliation.create_transaction(transaction_attrs) do
            {:ok, transaction} -> {[transaction | transactions], errors}
            {:error, changeset} ->
              error_msg = "Row #{row_number}: #{format_changeset_errors(changeset)}"
              {transactions, [error_msg | errors]}
          end

        {:error, error_msg} ->
          {transactions, ["Row #{row_number}: #{error_msg}" | errors]}
      end
    end)

    end_time = System.monotonic_time(:millisecond)
    processing_time = end_time - start_time

    # Final progress update
    progress_callback.(%{
      status: "complete",
      progress: 100,
      message: "Processing complete!",
      total_records: total_rows,
      records_processed: total_rows,
      processing_time: processing_time
    })

    {Enum.reverse(transactions), Enum.reverse(errors)}
  end

  # Process a single row and extract transaction data
  defp process_single_row(row, row_number, column_mapping, uploaded_file) do
    try do
      # Extract basic fields
      date = extract_date(row, column_mapping)
      amount = extract_amount(row, column_mapping)

      # Validate required fields
      if is_nil(amount) do
        {:error, "Missing or invalid amount"}
      else
        transaction_attrs = %{
          uploaded_file_id: uploaded_file.id,
          reconciliation_run_id: uploaded_file.reconciliation_run_id,
          row_number: row_number,
          transaction_date: date,
          transaction_id: extract_field(row, column_mapping, "transaction_id"),
          reference: extract_field(row, column_mapping, "reference"),
          description: extract_field(row, column_mapping, "description"),
          amount: amount,
          transaction_type: extract_field(row, column_mapping, "type"),
          account: extract_field(row, column_mapping, "account"),
          category: extract_field(row, column_mapping, "category"),
          raw_data: %{
            "original_row" => row,
            "row_number" => row_number
          }
        }

        {:ok, transaction_attrs}
      end
    rescue
      error ->
        {:error, "Processing error: #{Exception.message(error)}"}
    end
  end

  # Extract date from row
  defp extract_date(row, column_mapping) do
    case extract_field(row, column_mapping, "date") do
      nil -> nil
      date_str when is_binary(date_str) -> parse_date(date_str)
      date_val -> parse_date(to_string(date_val))
    end
  end

  # Extract amount from row (handle debit/credit columns)
  defp extract_amount(row, column_mapping) do
    cond do
      Map.has_key?(column_mapping, "amount") ->
        extract_field(row, column_mapping, "amount") |> parse_amount()

      Map.has_key?(column_mapping, "debit") and Map.has_key?(column_mapping, "credit") ->
        debit = extract_field(row, column_mapping, "debit") |> parse_amount()
        credit = extract_field(row, column_mapping, "credit") |> parse_amount()

        cond do
          debit && credit -> nil  # Invalid: both debit and credit
          debit -> Decimal.negate(debit)  # Debit is negative
          credit -> credit  # Credit is positive
          true -> nil
        end

      true -> nil
    end
  end

  # Extract field value from row
  defp extract_field(row, column_mapping, field) do
    case Map.get(column_mapping, field) do
      nil -> nil
      index when is_integer(index) ->
        case Enum.at(row, index) do
          nil -> nil
          "" -> nil
          value -> String.trim(to_string(value))
        end
    end
  end

  # Parse date string
  defp parse_date(date_str) when is_binary(date_str) do
    date_str = String.trim(date_str)

    # Try different date formats
    formats = [
      "{M}/{D}/{YYYY}",
      "{D}/{M}/{YYYY}",
      "{YYYY}-{M}-{D}",
      "{D}-{M}-{YYYY}",
      "{M}-{D}-{YYYY}"
    ]

    Enum.find_value(formats, fn format ->
      case Timex.parse(date_str, format) do
        {:ok, datetime} -> Timex.to_date(datetime)
        _ -> nil
      end
    end)
  end

  defp parse_date(_), do: nil

  # Parse amount string
  defp parse_amount(nil), do: nil
  defp parse_amount(""), do: nil
  defp parse_amount(amount_str) when is_binary(amount_str) do
    # Clean the amount string
    cleaned = amount_str
    |> String.trim()
    |> String.replace(~r/[$,\s]/, "")  # Remove currency symbols and commas
    |> String.replace("(", "-")       # Convert (123) to -123
    |> String.replace(")", "")

    case Decimal.parse(cleaned) do
      {decimal, ""} -> decimal
      _ -> nil
    end
  end

  defp parse_amount(amount) when is_number(amount) do
    Decimal.from_float(amount)
  end

  defp parse_amount(_), do: nil

  # Format changeset errors for display
  defp format_changeset_errors(changeset) do
    changeset.errors
    |> Enum.map(fn {field, {message, _}} -> "#{field} #{message}" end)
    |> Enum.join(", ")
  end
end
