<div class="max-w-4xl mx-auto p-6">
  <div class="mb-8">
    <h1 class="text-3xl font-bold text-gray-900 mb-2">Financial Reconciliation</h1>
    <p class="text-gray-600">Upload two files to compare and reconcile transactions</p>
  </div>

  <!-- Reconciliation Run Name -->
  <div class="mb-8 bg-white rounded-lg shadow-sm border p-6">
    <h2 class="text-lg font-semibold text-gray-900 mb-4">Reconciliation Details</h2>
    <.form for={@form} phx-change="update_name" class="space-y-4">
      <div>
        <.label for="name">Reconciliation Name</.label>
        <.input
          field={@form[:name]}
          type="text"
          placeholder="Enter a name for this reconciliation run"
          class="mt-1"
        />
      </div>
    </.form>
  </div>

  <!-- File Upload Section -->
  <.form for={@form} phx-submit="save" class="space-y-8">
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">

      <!-- File A Upload -->
      <div class="bg-white rounded-lg shadow-sm border p-6">
        <div class="flex items-center mb-4">
          <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mr-3">
            <span class="text-blue-600 font-semibold">A</span>
          </div>
          <h3 class="text-lg font-semibold text-gray-900">Source File A</h3>
        </div>

        <p class="text-sm text-gray-600 mb-4">
          Upload your first file (e.g., bank statements, invoices)
        </p>

        <div class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-blue-400 hover:bg-blue-50 transition-all duration-200 cursor-pointer">
          <.live_file_input upload={@uploads.file_a} class="hidden" />

          <%= if @file_a_uploaded do %>
            <div class="text-green-600">
              <.icon name="hero-check-circle" class="w-12 h-12 mx-auto mb-2" />
              <p class="font-medium"><%= @file_a_uploaded.original_filename %></p>
              <p class="text-sm text-gray-500">File uploaded successfully</p>
            </div>
          <% else %>
            <div class="text-gray-500 hover:text-blue-600 transition-colors">
              <.icon name="hero-document-arrow-up" class="w-12 h-12 mx-auto mb-2" />
              <p class="font-medium">Click to upload File A</p>
              <p class="text-sm">Excel (.xlsx, .xls) or CSV files up to 50MB</p>
              <p class="text-xs mt-2 text-gray-400">Or drag and drop files here</p>
            </div>
          <% end %>
        </div>

        <!-- File A Upload Progress -->
        <div :for={entry <- @uploads.file_a.entries} class="mt-4">
          <div class="flex items-center justify-between text-sm">
            <span class="font-medium"><%= entry.client_name %></span>
            <button
              type="button"
              phx-click="cancel_upload"
              phx-value-ref={entry.ref}
              phx-value-type="file_a"
              class="text-red-500 hover:text-red-700"
            >
              <.icon name="hero-x-mark" class="w-4 h-4" />
            </button>
          </div>
          <div class="w-full bg-gray-200 rounded-full h-2 mt-1">
            <div class="bg-blue-600 h-2 rounded-full transition-all duration-300" style={"width: #{entry.progress}%"}></div>
          </div>
        </div>

        <!-- File A Errors -->
        <div :for={{_ref, err} <- @uploads.file_a.errors} class="mt-2 text-red-600 text-sm">
          <.icon name="hero-exclamation-triangle" class="w-4 h-4 inline mr-1" />
          <%= error_to_string(err) %>
        </div>
      </div>

      <!-- File B Upload -->
      <div class="bg-white rounded-lg shadow-sm border p-6">
        <div class="flex items-center mb-4">
          <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center mr-3">
            <span class="text-green-600 font-semibold">B</span>
          </div>
          <h3 class="text-lg font-semibold text-gray-900">Source File B</h3>
        </div>

        <p class="text-sm text-gray-600 mb-4">
          Upload your second file (e.g., receipts, ledger entries)
        </p>

        <div class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-green-400 hover:bg-green-50 transition-all duration-200 cursor-pointer">
          <.live_file_input upload={@uploads.file_b} class="hidden" />

          <%= if @file_b_uploaded do %>
            <div class="text-green-600">
              <.icon name="hero-check-circle" class="w-12 h-12 mx-auto mb-2" />
              <p class="font-medium"><%= @file_b_uploaded.original_filename %></p>
              <p class="text-sm text-gray-500">File uploaded successfully</p>
            </div>
          <% else %>
            <div class="text-gray-500 hover:text-green-600 transition-colors">
              <.icon name="hero-document-arrow-up" class="w-12 h-12 mx-auto mb-2" />
              <p class="font-medium">Click to upload File B</p>
              <p class="text-sm">Excel (.xlsx, .xls) or CSV files up to 50MB</p>
              <p class="text-xs mt-2 text-gray-400">Or drag and drop files here</p>
            </div>
          <% end %>
        </div>

        <!-- File B Upload Progress -->
        <div :for={entry <- @uploads.file_b.entries} class="mt-4">
          <div class="flex items-center justify-between text-sm">
            <span class="font-medium"><%= entry.client_name %></span>
            <button
              type="button"
              phx-click="cancel_upload"
              phx-value-ref={entry.ref}
              phx-value-type="file_b"
              class="text-red-500 hover:text-red-700"
            >
              <.icon name="hero-x-mark" class="w-4 h-4" />
            </button>
          </div>
          <div class="w-full bg-gray-200 rounded-full h-2 mt-1">
            <div class="bg-green-600 h-2 rounded-full transition-all duration-300" style={"width: #{entry.progress}%"}></div>
          </div>
        </div>

        <!-- File B Errors -->
        <div :for={{_ref, err} <- @uploads.file_b.errors} class="mt-2 text-red-600 text-sm">
          <.icon name="hero-exclamation-triangle" class="w-4 h-4 inline mr-1" />
          <%= error_to_string(err) %>
        </div>
      </div>
    </div>

    <!-- Submit Button -->
    <div class="text-center">
      <%= if @file_a_uploaded && @file_b_uploaded do %>
        <.button
          type="submit"
          phx-disable-with="Processing..."
          class="bg-indigo-600 hover:bg-indigo-700 text-white px-8 py-3 text-lg font-medium"
        >
          <.icon name="hero-play" class="w-5 h-5 mr-2" />
          Start Reconciliation
        </.button>
      <% else %>
        <p class="text-gray-500 text-sm">
          Please upload both files to continue
        </p>
      <% end %>
    </div>
  </.form>

  <!-- Instructions -->
  <div class="mt-12 bg-gray-50 rounded-lg p-6">
    <h3 class="text-lg font-semibold text-gray-900 mb-4">How it works</h3>
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
      <div class="text-center">
        <div class="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-3">
          <.icon name="hero-document-arrow-up" class="w-6 h-6 text-blue-600" />
        </div>
        <h4 class="font-medium text-gray-900 mb-2">1. Upload Files</h4>
        <p class="text-sm text-gray-600">Upload two Excel or CSV files containing transaction data</p>
      </div>
      <div class="text-center">
        <div class="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-3">
          <.icon name="hero-magnifying-glass" class="w-6 h-6 text-green-600" />
        </div>
        <h4 class="font-medium text-gray-900 mb-2">2. Auto-Match</h4>
        <p class="text-sm text-gray-600">Our system automatically finds matching transactions</p>
      </div>
      <div class="text-center">
        <div class="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-3">
          <.icon name="hero-chart-bar" class="w-6 h-6 text-purple-600" />
        </div>
        <h4 class="font-medium text-gray-900 mb-2">3. Review Results</h4>
        <p class="text-sm text-gray-600">Get detailed reconciliation reports and insights</p>
      </div>
    </div>
  </div>
</div>