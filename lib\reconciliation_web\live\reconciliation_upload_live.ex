defmodule ReconciliationWeb.ReconciliationUploadLive do
  use ReconciliationWeb, :live_view
  import Phoenix.Component

  alias Reconciliation.Services.{ExcelParser, MatchingEngine}

  @impl true
  def mount(_params, _session, socket) do
    user = socket.assigns.current_user

    # Create a new reconciliation run
    {:ok, run} = Reconciliation.create_reconciliation_run(%{
      name: "Reconciliation #{DateTime.utc_now() |> DateTime.to_date()}",
      user_id: user.id,
      status: "pending"
    })

    # Subscribe to upload progress updates
    Phoenix.PubSub.subscribe(Reconciliation.PubSub, "upload_progress:#{run.id}")

    {:ok,
     socket
     |> assign(:page_title, "Upload Reconciliation Files")
     |> assign(:reconciliation_run, run)
     |> assign(:file_a_uploaded, nil)
     |> assign(:file_b_uploaded, nil)
     |> assign(:processing_status, %{file_a: nil, file_b: nil})
     |> assign(:upload_status, %{})
     |> assign(:database_status, %{})
     |> assign(:form, to_form(%{"name" => run.name}))
     |> allow_upload(:file_a,
          accept: ~w(.xlsx .xls .csv),
          max_entries: 1,
          max_file_size: 50_000_000, # 50MB
          auto_upload: true
        )
     |> allow_upload(:file_b,
          accept: ~w(.xlsx .xls .csv),
          max_entries: 1,
          max_file_size: 50_000_000, # 50MB
          auto_upload: true
        )
    }
  end

  @impl true
  def handle_event("validate", _params, socket) do
    {:noreply, socket}
  end

  def handle_progress(:file_a, entry, socket) do
    socket = handle_progress_update(entry, socket)
    {:noreply, socket}
  end

  def handle_progress(:file_b, entry, socket) do
    socket = handle_progress_update(entry, socket)
    {:noreply, socket}
  end

  @impl true
  def handle_event("update_name", %{"name" => name}, socket) do
    {:ok, run} = Reconciliation.update_reconciliation_run(socket.assigns.reconciliation_run, %{name: name})

    {:noreply,
     socket
     |> assign(:reconciliation_run, run)
     |> assign(:form, to_form(%{"name" => name}))
    }
  end

  @impl true
  def handle_event("save", _params, socket) do
    # Process file uploads
    file_a_results = consume_uploaded_entries(socket, :file_a, &handle_file_upload(&1, &2, "file_a", socket))
    file_b_results = consume_uploaded_entries(socket, :file_b, &handle_file_upload(&1, &2, "file_b", socket))

    socket =
      socket
      |> update_file_status(:file_a, file_a_results)
      |> update_file_status(:file_b, file_b_results)

    # Check if both files are uploaded
    if socket.assigns.file_a_uploaded && socket.assigns.file_b_uploaded do
      # Start processing files
      Task.start(fn -> process_reconciliation(socket.assigns.reconciliation_run.id) end)

      socket = put_flash(socket, :info, "Files uploaded successfully! Processing reconciliation...")
      {:noreply, push_navigate(socket, to: ~p"/reconciliation/#{socket.assigns.reconciliation_run.id}/results")}
    else
      {:noreply, socket}
    end
  end

  # Handle upload cancellations
  @impl true
  def handle_event("cancel_upload", %{"ref" => ref, "type" => "file_a"}, socket) do
    {:noreply, cancel_upload(socket, :file_a, ref)}
  end

  def handle_event("cancel_upload", %{"ref" => ref, "type" => "file_b"}, socket) do
    {:noreply, cancel_upload(socket, :file_b, ref)}
  end

  def handle_event("cancel_upload", %{"ref" => ref}, socket) do
    # Fallback for old format
    socket =
      socket
      |> cancel_upload(:file_a, ref)
      |> cancel_upload(:file_b, ref)
    {:noreply, socket}
  end

  # Handle PubSub messages for database insertion progress
  @impl true
  def handle_info({:database_progress, file_id, progress}, socket) do
    database_status = Map.put(socket.assigns.database_status, file_id, progress)
    {:noreply, assign(socket, :database_status, database_status)}
  end

  def handle_info({:database_complete, file_id, result}, socket) do
    database_status = Map.put(socket.assigns.database_status, file_id, %{
      status: "complete",
      result: result,
      completed_at: DateTime.utc_now()
    })
    {:noreply, assign(socket, :database_status, database_status)}
  end

  def handle_info({:database_error, file_id, error}, socket) do
    database_status = Map.put(socket.assigns.database_status, file_id, %{
      status: "error",
      error: error,
      failed_at: DateTime.utc_now()
    })

    # Show user-friendly error message
    socket = put_flash(socket, :error, "Failed to process file: #{error}")

    {:noreply, assign(socket, :database_status, database_status)}
  end

  # Handle retry upload event
  @impl true
  def handle_event("retry_upload", %{"file_id" => file_id}, socket) do
    # Find the uploaded file and retry processing
    case Reconciliation.get_uploaded_file(file_id) do
      nil ->
        {:noreply, put_flash(socket, :error, "File not found")}

      uploaded_file ->
        # Reset database status
        database_status = Map.delete(socket.assigns.database_status, uploaded_file.id)
        socket = assign(socket, :database_status, database_status)

        # Restart processing
        Task.start(fn ->
          process_file_with_progress(uploaded_file, socket.assigns.reconciliation_run.id)
        end)

        {:noreply, put_flash(socket, :info, "Retrying file processing...")}
    end
  end

  # Handle upload progress updates
  defp handle_progress_update(entry, socket) do
    upload_status = Map.get(socket.assigns.upload_status, entry.ref, %{})

    # Calculate upload speed and ETA
    current_time = System.monotonic_time(:millisecond)
    bytes_uploaded = trunc(entry.progress / 100 * entry.client_size)

    updated_status =
      case Map.get(upload_status, :start_time) do
        nil ->
          Map.merge(upload_status, %{
            start_time: current_time,
            bytes_uploaded: bytes_uploaded,
            upload_speed: 0,
            eta_seconds: nil
          })

        start_time ->
          elapsed_ms = current_time - start_time
          if elapsed_ms > 0 do
            upload_speed = bytes_uploaded / (elapsed_ms / 1000) # bytes per second
            remaining_bytes = entry.client_size - bytes_uploaded
            eta_seconds = if upload_speed > 0, do: trunc(remaining_bytes / upload_speed), else: nil

            Map.merge(upload_status, %{
              bytes_uploaded: bytes_uploaded,
              upload_speed: upload_speed,
              eta_seconds: eta_seconds
            })
          else
            upload_status
          end
      end

    assign(socket, :upload_status, Map.put(socket.assigns.upload_status, entry.ref, updated_status))
  end

  # Handle file upload and create database record
  defp handle_file_upload(%{path: path} = _meta, entry, file_type, socket) do
    try do
      # Validate file exists and is readable
      case File.stat(path) do
        {:ok, %{size: size}} when size > 0 ->
          # Create uploaded file record
          file_attrs = %{
            reconciliation_run_id: socket.assigns.reconciliation_run.id,
            file_type: file_type,
            filename: generate_filename(entry.client_name),
            original_filename: entry.client_name,
            file_size: size,
            mime_type: entry.client_type,
            file_path: path,
            status: "uploaded"
          }

          case Reconciliation.create_uploaded_file(file_attrs) do
            {:ok, uploaded_file} ->
              # Start database insertion process with progress tracking
              Task.start(fn ->
                process_file_with_progress(uploaded_file, socket.assigns.reconciliation_run.id)
              end)
              {:ok, uploaded_file}
            {:error, changeset} ->
              error_msg = format_changeset_errors(changeset)
              {:error, "Failed to save file information: #{error_msg}"}
          end

        {:ok, %{size: 0}} ->
          {:error, "File is empty"}

        {:error, reason} ->
          {:error, "Cannot read file: #{inspect(reason)}"}
      end
    rescue
      error ->
        {:error, "Upload failed: #{Exception.message(error)}"}
    end
  end

  # Update file status in socket assigns
  defp update_file_status(socket, file_type, results) do
    case results do
      [{:ok, uploaded_file}] ->
        assign(socket, String.to_atom("#{file_type}_uploaded"), uploaded_file)
      _ ->
        socket
    end
  end

  # Generate unique filename
  defp generate_filename(original_name) do
    timestamp = DateTime.utc_now() |> DateTime.to_unix()
    extension = Path.extname(original_name)
    base_name = Path.basename(original_name, extension)
    "#{base_name}_#{timestamp}#{extension}"
  end

  # Process file with progress tracking
  defp process_file_with_progress(uploaded_file, reconciliation_run_id) do
    try do
      # Validate file still exists
      unless File.exists?(uploaded_file.file_path) do
        raise "File no longer exists: #{uploaded_file.file_path}"
      end

      # Broadcast start of database insertion
      Phoenix.PubSub.broadcast(
        Reconciliation.PubSub,
        "upload_progress:#{reconciliation_run_id}",
        {:database_progress, uploaded_file.id, %{
          status: "inserting",
          progress: 0,
          message: "Starting database insertion..."
        }}
      )

      # Update file status to processing
      case Reconciliation.update_uploaded_file(uploaded_file, %{status: "processing"}) do
        {:ok, _} -> :ok
        {:error, changeset} ->
          error_msg = format_changeset_errors(changeset)
          raise "Failed to update file status: #{error_msg}"
      end

      # Parse file with progress tracking
      case ExcelParser.parse_file_with_progress(uploaded_file, fn progress ->
        Phoenix.PubSub.broadcast(
          Reconciliation.PubSub,
          "upload_progress:#{reconciliation_run_id}",
          {:database_progress, uploaded_file.id, progress}
        )
      end) do
        {:ok, result} ->
          # Broadcast completion
          Phoenix.PubSub.broadcast(
            Reconciliation.PubSub,
            "upload_progress:#{reconciliation_run_id}",
            {:database_complete, uploaded_file.id, result}
          )

        {:error, error} ->
          # Mark file as failed and broadcast error
          Reconciliation.mark_file_failed(uploaded_file, [error])

          Phoenix.PubSub.broadcast(
            Reconciliation.PubSub,
            "upload_progress:#{reconciliation_run_id}",
            {:database_error, uploaded_file.id, format_user_friendly_error(error)}
          )
      end

    rescue
      error ->
        # Mark file as failed
        Reconciliation.mark_file_failed(uploaded_file, [Exception.message(error)])

        Phoenix.PubSub.broadcast(
          Reconciliation.PubSub,
          "upload_progress:#{reconciliation_run_id}",
          {:database_error, uploaded_file.id, format_user_friendly_error(Exception.message(error))}
        )
    end
  end

  # Process reconciliation in background
  defp process_reconciliation(reconciliation_run_id) do
    run = Reconciliation.get_reconciliation_run!(reconciliation_run_id)
    uploaded_files = Reconciliation.get_uploaded_files(reconciliation_run_id)

    try do
      # Update status to processing
      Reconciliation.update_reconciliation_run(run, %{status: "processing"})

      # Parse both files
      Enum.each(uploaded_files, fn file ->
        ExcelParser.parse_file(file)
      end)

      # Perform transaction matching
      MatchingEngine.match_transactions(run)

    rescue
      error ->
        Reconciliation.mark_reconciliation_failed(run, Exception.message(error))
    end
  end

  defp error_to_string(:too_large), do: "File too large (max 50MB)"
  defp error_to_string(:too_many_files), do: "Too many files"
  defp error_to_string(:not_accepted), do: "File type not accepted (.xlsx, .xls, .csv only)"
  defp error_to_string(_), do: "Unknown error"

  # Error Alert Component
  defp error_alert(assigns) do
    ~H"""
    <div class="bg-red-50 border border-red-200 rounded-lg p-3">
      <div class="flex items-start">
        <div class="flex-shrink-0">
          <.icon name="hero-exclamation-triangle" class="w-5 h-5 text-red-400" />
        </div>
        <div class="ml-3 flex-1">
          <h3 class="text-sm font-medium text-red-800">
            Upload Error
          </h3>
          <div class="mt-1 text-sm text-red-700">
            <%= error_to_string(@error) %>
          </div>
          <div class="mt-2">
            <button
              type="button"
              class="text-xs bg-red-100 hover:bg-red-200 text-red-800 px-2 py-1 rounded transition-colors"
              phx-click="dismiss_error"
            >
              Dismiss
            </button>
          </div>
        </div>
      </div>
    </div>
    """
  end

  # Format user-friendly error messages
  defp format_user_friendly_error(error) when is_binary(error) do
    cond do
      String.contains?(error, "Unsupported file format") ->
        "This file format is not supported. Please upload an Excel (.xlsx, .xls) or CSV file."

      String.contains?(error, "Failed to parse") ->
        "Unable to read the file. Please check that the file is not corrupted and try again."

      String.contains?(error, "File is empty") ->
        "The uploaded file appears to be empty. Please check your file and try again."

      String.contains?(error, "Missing or invalid amount") ->
        "Some rows are missing required amount values. Please check your data and try again."

      String.contains?(error, "no data") ->
        "No transaction data found in the file. Please check that your file contains the expected data."

      true ->
        "An error occurred while processing your file. Please try again or contact support if the problem persists."
    end
  end
  defp format_user_friendly_error(error), do: "An unexpected error occurred: #{inspect(error)}"

  # Format changeset errors for display
  defp format_changeset_errors(changeset) do
    changeset.errors
    |> Enum.map(fn {field, {message, _}} -> "#{field}: #{message}" end)
    |> Enum.join(", ")
  end

  # Upload Progress Card Component
  defp upload_progress_card(assigns) do
    ~H"""
    <div class="bg-white border border-gray-200 rounded-lg p-4 shadow-sm">
      <!-- File Info Header -->
      <div class="flex items-center justify-between mb-3">
        <div class="flex items-center space-x-3">
          <div class="flex-shrink-0">
            <.icon name="hero-document" class="w-5 h-5 text-gray-400" />
          </div>
          <div class="min-w-0 flex-1">
            <p class="text-sm font-medium text-gray-900 truncate">
              <%= @entry.client_name %>
            </p>
            <p class="text-xs text-gray-500">
              <%= format_file_size(@entry.client_size) %>
            </p>
          </div>
        </div>
        <button
          type="button"
          phx-click="cancel_upload"
          phx-value-ref={@entry.ref}
          phx-value-type={@file_type}
          class="text-gray-400 hover:text-red-500 transition-colors"
        >
          <.icon name="hero-x-mark" class="w-4 h-4" />
        </button>
      </div>

      <!-- Upload Progress Section -->
      <div class="mb-4">
        <div class="flex items-center justify-between text-xs text-gray-600 mb-2">
          <span class="font-medium">File Upload</span>
          <span class="font-mono"><%= @entry.progress %>%</span>
        </div>
        <div class="w-full bg-gray-200 rounded-full h-3">
          <div
            class={"h-3 rounded-full transition-all duration-300 #{progress_bar_color(@file_type)}"}
            style={"width: #{@entry.progress}%"}
          ></div>
        </div>

        <!-- Upload Stats -->
        <%= if @entry.progress > 0 and @entry.progress < 100 do %>
          <div class="grid grid-cols-2 gap-4 text-xs text-gray-500 mt-2">
            <div>
              <span class="font-medium">Speed:</span>
              <%= format_upload_speed(Map.get(@upload_status, :upload_speed, 0)) %>
            </div>
            <div>
              <span class="font-medium">ETA:</span>
              <%= format_eta(Map.get(@upload_status, :eta_seconds)) %>
            </div>
          </div>
        <% end %>

        <!-- Upload Status Messages -->
        <%= if @entry.progress == 100 do %>
          <div class="flex items-center text-xs text-green-600 mt-2">
            <.icon name="hero-check-circle" class="w-4 h-4 mr-1" />
            <span>Upload complete - Processing file...</span>
          </div>
        <% end %>
      </div>

      <!-- Database Processing Section -->
      <%= if @database_status do %>
        <div class="border-t border-gray-200 pt-4">
          <div class="flex items-center justify-between text-xs text-gray-600 mb-2">
            <span class="font-medium">Database Processing</span>
            <%= if Map.get(@database_status, :progress) do %>
              <span class="font-mono"><%= @database_status.progress %>%</span>
            <% end %>
          </div>

          <.database_status_indicator status={@database_status} file_type={@file_type} />
        </div>
      <% end %>
    </div>
    """
  end

  # Database Status Indicator Component
  defp database_status_indicator(assigns) do
    ~H"""
    <div class="space-y-3">
      <%= case @status.status do %>
        <% "inserting" -> %>
          <div class="space-y-2">
            <div class="flex items-center justify-between">
              <div class="flex items-center text-sm text-blue-600">
                <.icon name="hero-arrow-path" class="w-4 h-4 mr-2 animate-spin" />
                <span><%= @status.message || "Inserting records into database..." %></span>
              </div>
              <%= if Map.get(@status, :records_processed) && Map.get(@status, :total_records) do %>
                <span class="text-xs text-gray-500 font-mono">
                  <%= @status.records_processed %>/<%= @status.total_records %>
                </span>
              <% end %>
            </div>
            <%= if Map.has_key?(@status, :progress) and @status.progress > 0 do %>
              <div class="w-full bg-gray-200 rounded-full h-2">
                <div class={"bg-blue-600 h-2 rounded-full transition-all duration-300 #{progress_bar_color(@file_type)}"} style={"width: #{@status.progress}%"}></div>
              </div>
              <div class="flex justify-between text-xs text-gray-500">
                <span>Processing records...</span>
                <span><%= @status.progress %>%</span>
              </div>
            <% end %>
          </div>

        <% "parsing" -> %>
          <div class="space-y-2">
            <div class="flex items-center text-sm text-blue-600">
              <.icon name="hero-arrow-path" class="w-4 h-4 mr-2 animate-spin" />
              <span><%= @status.message || "Parsing file structure..." %></span>
            </div>
            <%= if Map.has_key?(@status, :progress) and @status.progress > 0 do %>
              <div class="w-full bg-gray-200 rounded-full h-2">
                <div class={"bg-blue-600 h-2 rounded-full transition-all duration-300 #{progress_bar_color(@file_type)}"} style={"width: #{@status.progress}%"}></div>
              </div>
              <div class="flex justify-between text-xs text-gray-500">
                <span>Reading file content...</span>
                <span><%= @status.progress %>%</span>
              </div>
            <% end %>
          </div>

        <% "processing" -> %>
          <div class="space-y-2">
            <div class="flex items-center justify-between">
              <div class="flex items-center text-sm text-blue-600">
                <.icon name="hero-arrow-path" class="w-4 h-4 mr-2 animate-spin" />
                <span><%= @status.message || "Processing transaction data..." %></span>
              </div>
              <%= if Map.get(@status, :records_processed) && Map.get(@status, :total_records) do %>
                <span class="text-xs text-gray-500 font-mono">
                  <%= @status.records_processed %>/<%= @status.total_records %>
                </span>
              <% end %>
            </div>
            <%= if Map.has_key?(@status, :progress) and @status.progress > 0 do %>
              <div class="w-full bg-gray-200 rounded-full h-2">
                <div class={"bg-blue-600 h-2 rounded-full transition-all duration-300 #{progress_bar_color(@file_type)}"} style={"width: #{@status.progress}%"}></div>
              </div>
              <div class="flex justify-between text-xs text-gray-500">
                <span>Validating and storing records...</span>
                <span><%= @status.progress %>%</span>
              </div>
            <% end %>
          </div>

        <% "complete" -> %>
          <div class="space-y-2">
            <div class="flex items-center text-sm text-green-600">
              <.icon name="hero-check-circle" class="w-4 h-4 mr-2" />
              <span>Database processing complete!</span>
            </div>
            <div class="w-full bg-gray-200 rounded-full h-2">
              <div class={"bg-green-600 h-2 rounded-full #{progress_bar_color(@file_type)}"} style="width: 100%"></div>
            </div>
            <%= if Map.has_key?(@status, :result) do %>
              <div class="grid grid-cols-2 gap-4 text-xs text-gray-600">
                <%= if Map.get(@status.result, :rows_inserted) do %>
                  <div>
                    <span class="font-medium">Records:</span>
                    <%= @status.result.rows_inserted %>
                  </div>
                <% end %>
                <%= if Map.get(@status.result, :processing_time) do %>
                  <div>
                    <span class="font-medium">Time:</span>
                    <%= format_processing_time(@status.result.processing_time) %>
                  </div>
                <% end %>
              </div>
            <% end %>
          </div>

        <% "error" -> %>
          <div class="space-y-2">
            <div class="flex items-center justify-between">
              <div class="flex items-center text-sm text-red-600">
                <.icon name="hero-exclamation-triangle" class="w-4 h-4 mr-2" />
                <span>Processing failed</span>
              </div>
              <button
                type="button"
                phx-click="retry_upload"
                phx-value-file_id={get_file_id_from_status(@status)}
                class="text-xs bg-red-100 hover:bg-red-200 text-red-800 px-3 py-1 rounded transition-colors font-medium"
              >
                Retry
              </button>
            </div>
            <div class="w-full bg-gray-200 rounded-full h-2">
              <div class="bg-red-600 h-2 rounded-full" style="width: 100%"></div>
            </div>
            <%= if Map.has_key?(@status, :error) do %>
              <div class="bg-red-50 border border-red-200 rounded p-2">
                <div class="text-xs text-red-700 font-medium mb-1">Error Details:</div>
                <div class="text-xs text-red-600">
                  <%= @status.error %>
                </div>
              </div>
            <% end %>
          </div>

        <% _ -> %>
          <div class="space-y-2">
            <div class="flex items-center text-sm text-gray-500">
              <.icon name="hero-clock" class="w-4 h-4 mr-2" />
              <span>Waiting to start database processing...</span>
            </div>
            <div class="w-full bg-gray-200 rounded-full h-2">
              <div class="bg-gray-400 h-2 rounded-full" style="width: 0%"></div>
            </div>
          </div>
      <% end %>
    </div>
    """
  end

  # Helper functions for formatting
  defp format_file_size(bytes) when is_integer(bytes) do
    cond do
      bytes >= 1_000_000 -> "#{Float.round(bytes / 1_000_000, 1)} MB"
      bytes >= 1_000 -> "#{Float.round(bytes / 1_000, 1)} KB"
      true -> "#{bytes} bytes"
    end
  end
  defp format_file_size(_), do: "Unknown size"

  defp format_upload_speed(speed) when is_number(speed) and speed > 0 do
    cond do
      speed >= 1_000_000 -> "#{Float.round(speed / 1_000_000, 1)} MB/s"
      speed >= 1_000 -> "#{Float.round(speed / 1_000, 1)} KB/s"
      true -> "#{trunc(speed)} B/s"
    end
  end
  defp format_upload_speed(_), do: "Calculating..."

  defp format_eta(nil), do: "Calculating..."
  defp format_eta(seconds) when is_integer(seconds) and seconds > 0 do
    cond do
      seconds >= 60 -> "#{div(seconds, 60)}m #{rem(seconds, 60)}s"
      true -> "#{seconds}s"
    end
  end
  defp format_eta(_), do: "Almost done"

  defp progress_bar_color("file_a"), do: "bg-blue-600"
  defp progress_bar_color("file_b"), do: "bg-green-600"
  defp progress_bar_color(_), do: "bg-gray-600"

  defp format_processing_time(time_ms) when is_number(time_ms) do
    cond do
      time_ms >= 60_000 -> "#{Float.round(time_ms / 60_000, 1)}m"
      time_ms >= 1_000 -> "#{Float.round(time_ms / 1_000, 1)}s"
      true -> "#{trunc(time_ms)}ms"
    end
  end
  defp format_processing_time(_), do: "Unknown"

  # Helper function to get database status for a file entry
  defp get_database_status_for_entry(_database_status, nil), do: nil
  defp get_database_status_for_entry(database_status, uploaded_file) do
    Map.get(database_status, uploaded_file.id)
  end

  # Helper function to extract file ID from status (for retry functionality)
  defp get_file_id_from_status(status) do
    # This would need to be passed from the component context
    # For now, we'll use a placeholder
    Map.get(status, :file_id, "unknown")
  end

  # Handle dismiss error event
  @impl true
  def handle_event("dismiss_error", _params, socket) do
    {:noreply, clear_flash(socket, :error)}
  end
end
