<div class="max-w-4xl mx-auto p-6">
  <!-- Header -->
  <div class="mb-8">
    <h1 class="text-3xl font-bold text-gray-900 mb-2">Reconciliation Settings</h1>
    <p class="text-gray-600">Configure your reconciliation preferences and matching criteria</p>
  </div>

  <.form for={@form} phx-change="validate" phx-submit="save" class="space-y-8">
    <!-- Matching Criteria -->
    <div class="bg-white rounded-lg shadow-sm border p-6">
      <h2 class="text-xl font-semibold text-gray-900 mb-6">Matching Criteria</h2>
      
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <.label for={@form[:amount_tolerance].id}>Amount Tolerance</.label>
          <.input 
            field={@form[:amount_tolerance]} 
            type="number" 
            step="0.01"
            min="0"
            placeholder="0.01"
          />
          <p class="mt-1 text-sm text-gray-500">
            Maximum difference allowed for amount matching (e.g., 0.01 for $0.01)
          </p>
        </div>

        <div>
          <.label for={@form[:date_tolerance_days].id}>Date Tolerance (Days)</.label>
          <.input 
            field={@form[:date_tolerance_days]} 
            type="number" 
            min="0"
            placeholder="3"
          />
          <p class="mt-1 text-sm text-gray-500">
            Maximum number of days difference allowed for date matching
          </p>
        </div>

        <div>
          <.label for={@form[:fuzzy_match_threshold].id}>Fuzzy Match Threshold</.label>
          <.input 
            field={@form[:fuzzy_match_threshold]} 
            type="number" 
            step="0.01"
            min="0"
            max="1"
            placeholder="0.8"
          />
          <p class="mt-1 text-sm text-gray-500">
            Minimum similarity score for fuzzy matching (0.0 to 1.0)
          </p>
        </div>
      </div>
    </div>

    <!-- Auto-Matching Options -->
    <div class="bg-white rounded-lg shadow-sm border p-6">
      <h2 class="text-xl font-semibold text-gray-900 mb-6">Auto-Matching Options</h2>
      
      <div class="space-y-4">
        <div class="flex items-center">
          <input
            type="checkbox"
            id={@form[:auto_match_exact].id}
            name={@form[:auto_match_exact].name}
            value="true"
            checked={@form[:auto_match_exact].value}
            class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
          />
          <label for={@form[:auto_match_exact].id} class="ml-2 block text-sm text-gray-900">
            Automatically match exact transactions
          </label>
        </div>
        <p class="ml-6 text-sm text-gray-500">
          Automatically create matches for transactions with identical amounts, dates, and references
        </p>

        <div class="flex items-center">
          <input
            type="checkbox"
            id={@form[:auto_match_fuzzy].id}
            name={@form[:auto_match_fuzzy].name}
            value="true"
            checked={@form[:auto_match_fuzzy].value}
            class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
          />
          <label for={@form[:auto_match_fuzzy].id} class="ml-2 block text-sm text-gray-900">
            Automatically match similar transactions (fuzzy matching)
          </label>
        </div>
        <p class="ml-6 text-sm text-gray-500">
          Automatically create matches for transactions that meet the fuzzy match threshold
        </p>
      </div>
    </div>

    <!-- Default Preferences -->
    <div class="bg-white rounded-lg shadow-sm border p-6">
      <h2 class="text-xl font-semibold text-gray-900 mb-6">Default Preferences</h2>
      
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <.label for={@form[:default_currency].id}>Default Currency</.label>
          <select 
            id={@form[:default_currency].id}
            name={@form[:default_currency].name}
            class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
          >
            <%= for {value, label} <- currency_options() do %>
              <option value={value} selected={@form[:default_currency].value == value}>
                <%= label %>
              </option>
            <% end %>
          </select>
        </div>

        <div>
          <.label for={@form[:preferred_date_format].id}>Preferred Date Format</.label>
          <select 
            id={@form[:preferred_date_format].id}
            name={@form[:preferred_date_format].name}
            class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
          >
            <%= for {value, label} <- date_format_options() do %>
              <option value={value} selected={@form[:preferred_date_format].value == value}>
                <%= label %>
              </option>
            <% end %>
          </select>
        </div>
      </div>
    </div>

    <!-- Column Mapping Preferences -->
    <div class="bg-white rounded-lg shadow-sm border p-6">
      <h2 class="text-xl font-semibold text-gray-900 mb-6">Column Mapping Preferences</h2>
      <p class="text-gray-600 mb-4">
        These settings help the system automatically detect columns in your Excel/CSV files.
        The system will look for these column names when processing your files.
      </p>
      
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <h3 class="text-sm font-medium text-gray-900 mb-2">Date Column Names</h3>
          <p class="text-xs text-gray-500 mb-2">Common: date, transaction_date, date_of_transaction</p>
        </div>
        
        <div>
          <h3 class="text-sm font-medium text-gray-900 mb-2">Amount Column Names</h3>
          <p class="text-xs text-gray-500 mb-2">Common: amount, transaction_amount, debit, credit</p>
        </div>
        
        <div>
          <h3 class="text-sm font-medium text-gray-900 mb-2">Reference Column Names</h3>
          <p class="text-xs text-gray-500 mb-2">Common: reference, ref, transaction_reference</p>
        </div>
        
        <div>
          <h3 class="text-sm font-medium text-gray-900 mb-2">Description Column Names</h3>
          <p class="text-xs text-gray-500 mb-2">Common: description, details, memo, narration</p>
        </div>
      </div>
    </div>

    <!-- Action Buttons -->
    <div class="flex justify-between items-center pt-6 border-t border-gray-200">
      <button
        type="button"
        phx-click="reset_defaults"
        class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg font-medium"
      >
        Reset to Defaults
      </button>
      
      <div class="flex space-x-3">
        <.link navigate={~p"/dashboard"} class="bg-gray-300 hover:bg-gray-400 text-gray-700 px-4 py-2 rounded-lg font-medium">
          Cancel
        </.link>
        <.button type="submit" phx-disable-with="Saving..." class="bg-indigo-600 hover:bg-indigo-700 text-white">
          Save Settings
        </.button>
      </div>
    </div>
  </.form>
</div>
