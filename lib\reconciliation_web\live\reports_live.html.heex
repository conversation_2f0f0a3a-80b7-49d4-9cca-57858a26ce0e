<div class="max-w-7xl mx-auto p-6">
  <!-- Header -->
  <div class="mb-8">
    <div class="flex justify-between items-center">
      <div>
        <h1 class="text-3xl font-bold text-gray-900 mb-2">Reports & Analytics</h1>
        <p class="text-gray-600">Comprehensive insights into your reconciliation activities</p>
      </div>
      
      <!-- Period Filter -->
      <div>
        <select 
          phx-change="filter_period"
          name="period"
          class="rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
        >
          <option value="all" selected={@selected_period == "all"}>All Time</option>
          <option value="last_30" selected={@selected_period == "last_30"}>Last 30 Days</option>
          <option value="last_90" selected={@selected_period == "last_90"}>Last 90 Days</option>
          <option value="last_year" selected={@selected_period == "last_year"}>Last Year</option>
        </select>
      </div>
    </div>
  </div>

  <!-- Summary Statistics -->
  <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
    <div class="bg-white rounded-lg shadow-sm border p-6">
      <div class="flex items-center">
        <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
          <.icon name="hero-document-text" class="w-6 h-6 text-blue-600" />
        </div>
        <div class="ml-4">
          <p class="text-sm font-medium text-gray-600">Total Runs</p>
          <p class="text-2xl font-bold text-gray-900"><%= @summary_stats.total_runs %></p>
        </div>
      </div>
    </div>

    <div class="bg-white rounded-lg shadow-sm border p-6">
      <div class="flex items-center">
        <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
          <.icon name="hero-check-circle" class="w-6 h-6 text-green-600" />
        </div>
        <div class="ml-4">
          <p class="text-sm font-medium text-gray-600">Completed</p>
          <p class="text-2xl font-bold text-gray-900"><%= @summary_stats.completed_runs %></p>
        </div>
      </div>
    </div>

    <div class="bg-white rounded-lg shadow-sm border p-6">
      <div class="flex items-center">
        <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
          <.icon name="hero-chart-bar" class="w-6 h-6 text-purple-600" />
        </div>
        <div class="ml-4">
          <p class="text-sm font-medium text-gray-600">Avg Match Rate</p>
          <p class="text-2xl font-bold text-gray-900"><%= @summary_stats.avg_match_rate %>%</p>
        </div>
      </div>
    </div>

    <div class="bg-white rounded-lg shadow-sm border p-6">
      <div class="flex items-center">
        <div class="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center">
          <.icon name="hero-currency-dollar" class="w-6 h-6 text-yellow-600" />
        </div>
        <div class="ml-4">
          <p class="text-sm font-medium text-gray-600">Total Processed</p>
          <p class="text-2xl font-bold text-gray-900"><%= format_currency(@summary_stats.total_amount_processed) %></p>
        </div>
      </div>
    </div>
  </div>

  <!-- Reconciliation Runs Table -->
  <div class="bg-white rounded-lg shadow-sm border overflow-hidden">
    <div class="px-6 py-4 border-b border-gray-200">
      <h3 class="text-lg font-medium text-gray-900">Reconciliation History</h3>
    </div>
    
    <%= if Enum.any?(@reconciliation_runs) do %>
      <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Name
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Date
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Status
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Transactions
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Match Rate
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Total Amount
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <%= for run <- @reconciliation_runs do %>
              <tr class="hover:bg-gray-50">
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="text-sm font-medium text-gray-900"><%= run.name %></div>
                  <%= if run.description do %>
                    <div class="text-sm text-gray-500"><%= run.description %></div>
                  <% end %>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  <%= if run.processed_at do %>
                    <%= Calendar.strftime(run.processed_at, "%b %d, %Y") %>
                  <% else %>
                    <%= Calendar.strftime(run.inserted_at, "%b %d, %Y") %>
                  <% end %>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="flex items-center">
                    <.icon name={status_icon(run.status)} class={"w-4 h-4 mr-2 text-#{status_color(run.status)}-600"} />
                    <span class={"text-#{status_color(run.status)}-600 font-medium capitalize"}>
                      <%= run.status %>
                    </span>
                  </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  <%= run.total_transactions_a + run.total_transactions_b %>
                  <span class="text-gray-500">
                    (<%= run.matched_count %> matched)
                  </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <span class={[
                    "text-sm font-medium",
                    match_rate_color(Decimal.to_float(run.match_rate))
                  ]}>
                    <%= format_percentage(run.match_rate) %>
                  </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  <%= format_currency(Decimal.add(run.total_amount_a, run.total_amount_b)) %>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                  <%= if run.status == "completed" do %>
                    <.link 
                      navigate={~p"/reconciliation/#{run.id}/results"}
                      class="text-indigo-600 hover:text-indigo-900"
                    >
                      View
                    </.link>
                    <button
                      phx-click="export_report"
                      phx-value-run_id={run.id}
                      class="text-green-600 hover:text-green-900"
                    >
                      Export
                    </button>
                  <% else %>
                    <span class="text-gray-400">Processing...</span>
                  <% end %>
                </td>
              </tr>
            <% end %>
          </tbody>
        </table>
      </div>
    <% else %>
      <div class="text-center py-12">
        <.icon name="hero-chart-bar" class="w-12 h-12 text-gray-400 mx-auto mb-4" />
        <h3 class="text-lg font-medium text-gray-900 mb-2">No reconciliation data</h3>
        <p class="text-gray-500 mb-6">Start by creating a reconciliation run to see reports here.</p>
        <.link navigate={~p"/reconciliation"} class="bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-lg font-medium">
          Create Reconciliation Run
        </.link>
      </div>
    <% end %>
  </div>
</div>
