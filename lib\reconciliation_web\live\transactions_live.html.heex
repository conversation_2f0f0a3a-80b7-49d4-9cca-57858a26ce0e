<div class="max-w-7xl mx-auto p-6">
  <!-- Header -->
  <div class="mb-8">
    <h1 class="text-3xl font-bold text-gray-900 mb-2">Transactions</h1>
    <p class="text-gray-600">View and manage all transactions from your reconciliation runs</p>
  </div>

  <!-- Filters and Search -->
  <div class="bg-white rounded-lg shadow-sm border p-6 mb-8">
    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
      <!-- Reconciliation Run Filter -->
      <div>
        <label for="run_filter" class="block text-sm font-medium text-gray-700 mb-2">
          Filter by Reconciliation Run
        </label>
        <select 
          id="run_filter"
          phx-change="filter_by_run"
          name="run_id"
          class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
        >
          <option value="">All Reconciliation Runs</option>
          <%= for run <- @reconciliation_runs do %>
            <option value={run.id} selected={@selected_run_id == to_string(run.id)}>
              <%= run.name %> - <%= Calendar.strftime(run.inserted_at, "%b %d, %Y") %>
            </option>
          <% end %>
        </select>
      </div>

      <!-- Search -->
      <div>
        <label for="search" class="block text-sm font-medium text-gray-700 mb-2">
          Search Transactions
        </label>
        <.form for={%{}} phx-change="search" class="relative">
          <input
            type="text"
            name="search"
            value={@search_query}
            placeholder="Search by description, reference, or amount..."
            class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 pl-10"
          />
          <.icon name="hero-magnifying-glass" class="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
        </.form>
      </div>
    </div>
  </div>

  <!-- Transactions Table -->
  <div class="bg-white rounded-lg shadow-sm border overflow-hidden">
    <%= if Enum.any?(@transactions) do %>
      <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Date
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Description
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Reference
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Amount
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Source
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Status
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <%= for transaction <- @transactions do %>
              <tr class="hover:bg-gray-50">
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  <%= format_date(transaction.transaction_date) %>
                </td>
                <td class="px-6 py-4 text-sm text-gray-900">
                  <div class="max-w-xs truncate">
                    <%= transaction.description || "-" %>
                  </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  <%= transaction.reference || "-" %>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                  <span class={[
                    if Decimal.negative?(transaction.amount) do
                      "text-red-600"
                    else
                      "text-green-600"
                    end
                  ]}>
                    <%= format_currency(transaction.amount) %>
                  </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <%= file_type_badge(transaction.uploaded_file.file_type) %>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <%= match_status_badge(transaction.is_matched) %>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                  <.link 
                    navigate={~p"/reconciliation/#{transaction.reconciliation_run_id}/results"}
                    class="text-indigo-600 hover:text-indigo-900"
                  >
                    View Run
                  </.link>
                </td>
              </tr>
            <% end %>
          </tbody>
        </table>
      </div>

      <!-- Pagination placeholder -->
      <div class="bg-white px-4 py-3 border-t border-gray-200 sm:px-6">
        <div class="flex items-center justify-between">
          <div class="flex-1 flex justify-between sm:hidden">
            <!-- Mobile pagination controls -->
          </div>
          <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
            <div>
              <p class="text-sm text-gray-700">
                Showing <span class="font-medium"><%= length(@transactions) %></span> transactions
              </p>
            </div>
          </div>
        </div>
      </div>
    <% else %>
      <div class="text-center py-12">
        <.icon name="hero-document-text" class="w-12 h-12 text-gray-400 mx-auto mb-4" />
        <h3 class="text-lg font-medium text-gray-900 mb-2">No transactions found</h3>
        <p class="text-gray-500 mb-6">
          <%= if @selected_run_id || @search_query != "" do %>
            Try adjusting your filters or search criteria.
          <% else %>
            Start by creating a reconciliation run to see transactions here.
          <% end %>
        </p>
        <%= unless @selected_run_id || @search_query != "" do %>
          <.link navigate={~p"/reconciliation"} class="bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-lg font-medium">
            Create Reconciliation Run
          </.link>
        <% end %>
      </div>
    <% end %>
  </div>
</div>
