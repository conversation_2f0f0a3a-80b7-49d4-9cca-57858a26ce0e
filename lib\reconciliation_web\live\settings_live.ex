defmodule ReconciliationWeb.SettingsLive do
  use ReconciliationWeb, :live_view

  alias Reconciliation.ReconciliationSettings

  @impl true
  def mount(_params, _session, socket) do
    user = socket.assigns.current_user
    
    # Get or create settings for the user
    {:ok, settings} = Reconciliation.get_or_create_settings(user.id)
    
    {:ok,
     socket
     |> assign(:page_title, "Settings")
     |> assign(:settings, settings)
     |> assign(:form, to_form(ReconciliationSettings.changeset(settings, %{})))
     |> assign(:saved, false)
    }
  end

  @impl true
  def handle_event("validate", %{"reconciliation_settings" => settings_params}, socket) do
    changeset = ReconciliationSettings.changeset(socket.assigns.settings, settings_params)
    
    {:noreply, assign(socket, :form, to_form(changeset, action: :validate))}
  end

  def handle_event("save", %{"reconciliation_settings" => settings_params}, socket) do
    case Reconciliation.update_settings(socket.assigns.settings, settings_params) do
      {:ok, updated_settings} ->
        {:noreply,
         socket
         |> assign(:settings, updated_settings)
         |> assign(:form, to_form(ReconciliationSettings.changeset(updated_settings, %{})))
         |> assign(:saved, true)
         |> put_flash(:info, "Settings saved successfully!")
        }
      
      {:error, changeset} ->
        {:noreply, assign(socket, :form, to_form(changeset, action: :validate))}
    end
  end

  def handle_event("reset_defaults", _params, socket) do
    default_settings = ReconciliationSettings.default_settings()
    
    case Reconciliation.update_settings(socket.assigns.settings, default_settings) do
      {:ok, updated_settings} ->
        {:noreply,
         socket
         |> assign(:settings, updated_settings)
         |> assign(:form, to_form(ReconciliationSettings.changeset(updated_settings, %{})))
         |> put_flash(:info, "Settings reset to defaults!")
        }
      
      {:error, changeset} ->
        {:noreply, assign(socket, :form, to_form(changeset, action: :validate))}
    end
  end

  # Helper functions
  defp currency_options do
    ReconciliationSettings.supported_currencies()
  end

  defp date_format_options do
    ReconciliationSettings.supported_date_formats()
  end
end
