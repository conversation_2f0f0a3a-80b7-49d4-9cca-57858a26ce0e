// If you want to use Phoenix channels, run `mix help phx.gen.channel`
// to get started and then uncomment the line below.
// import "./user_socket.js"

// You can include dependencies in two ways.
//
// The simplest option is to put them in assets/vendor and
// import them using relative paths:
//
//     import "../vendor/some-package.js"
//
// Alternatively, you can `npm install some-package --prefix assets` and import
// them using a path starting with the package name:
//
//     import "some-package"
//

// Include phoenix_html to handle method=PUT/DELETE in forms and buttons.
import "phoenix_html"
// Establish Phoenix Socket and LiveView configuration.
import {Socket} from "phoenix"
import {LiveSocket} from "phoenix_live_view"
import topbar from "../vendor/topbar"

let csrfToken = document.querySelector("meta[name='csrf-token']").getAttribute("content")
let liveSocket = new LiveSocket("/live", Socket, {
  longPollFallbackMs: 2500,
  params: {_csrf_token: csrfToken}
})

// Show progress bar on live navigation and form submits
topbar.config({barColors: {0: "#29d"}, shadowColor: "rgba(0, 0, 0, .3)"})
window.addEventListener("phx:page-loading-start", _info => topbar.show(300))
window.addEventListener("phx:page-loading-stop", _info => topbar.hide())

// connect if there are any LiveViews on the page
liveSocket.connect()

// expose liveSocket on window for web console debug logs and latency simulation:
// >> liveSocket.enableDebug()
// >> liveSocket.enableLatencySim(1000)  // enabled for duration of browser session
// >> liveSocket.disableLatencySim()
window.liveSocket = liveSocket

// Make upload areas clickable
document.addEventListener('DOMContentLoaded', function() {
  // Function to make upload areas clickable
  function makeUploadAreasClickable() {
    // Find all upload areas (divs with border-dashed that contain hidden file inputs)
    const uploadAreas = document.querySelectorAll('.border-dashed');

    uploadAreas.forEach(uploadArea => {
      // Find the hidden file input within this upload area
      const fileInput = uploadArea.querySelector('input[type="file"]');

      if (fileInput && fileInput.classList.contains('hidden')) {
        // Make the upload area clickable
        uploadArea.style.cursor = 'pointer';

        // Add click event listener
        uploadArea.addEventListener('click', function(e) {
          // Prevent event bubbling
          e.preventDefault();
          e.stopPropagation();

          // Trigger the hidden file input
          fileInput.click();
        });

        // Add drag and drop functionality
        uploadArea.addEventListener('dragover', function(e) {
          e.preventDefault();
          e.stopPropagation();
          uploadArea.classList.add('border-blue-400', 'bg-blue-50');
        });

        uploadArea.addEventListener('dragleave', function(e) {
          e.preventDefault();
          e.stopPropagation();
          uploadArea.classList.remove('border-blue-400', 'bg-blue-50');
        });

        uploadArea.addEventListener('drop', function(e) {
          e.preventDefault();
          e.stopPropagation();
          uploadArea.classList.remove('border-blue-400', 'bg-blue-50');

          // Handle dropped files
          const files = e.dataTransfer.files;
          if (files.length > 0) {
            // Set the files to the file input
            fileInput.files = files;
            // Trigger change event
            const event = new Event('change', { bubbles: true });
            fileInput.dispatchEvent(event);
          }
        });
      }
    });
  }

  // Initial setup
  makeUploadAreasClickable();

  // Re-setup after LiveView updates
  document.addEventListener('phx:update', makeUploadAreasClickable);
});

